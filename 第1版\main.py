from agent import DQNAgent
from environment import Environment
import os

class Config:
    # 网络参数
    learning_rate = 0.0003
    gamma = 0.97
    
    # 经验回放参数
    buffer_size = 30000
    batch_size = 32
    
    # 目标网络同步频率
    target_sync = 100
    
    # 训练参数
    train_episodes = 500
    
    # 测试参数
    test_episodes = 5
    
    # 环境参数
    mode = 'B0B41X9SMT'  # 'linear', 'sin', 'poisson', 'B0B41X9SMT', 'B0BG1H99FB'
    
    # 模型保存路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    @property
    def model_filename(self):
        return os.path.join(self.script_dir, 'model', f'{self.mode}_best_model.pth')

def create_agent():
    config = Config()
    
    # 创建环境
    env = Environment()
    state_dim = 19  # 4个基础维度 + 15个历史销量维度
    action_dim = env.action_space['n']  # 31
    
    # 创建Agent
    agent = DQNAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        learning_rate=config.learning_rate,
        gamma=config.gamma,
        buffer_size=config.buffer_size,
        batch_size=config.batch_size,
        target_sync=config.target_sync
    )
    
    return agent, env, config

def train():
    agent, env, config = create_agent()
    
    # 训练
    train_scores, eval_scores, losses = agent.train(env, config)
    
    return train_scores, eval_scores, losses

def test():
    agent, env, config = create_agent()
    
    # 加载训练好的最佳模型
    agent.load_model(config.model_filename)
    test_scores, avg_score = agent.test(env, config, episodes=config.test_episodes)

    return test_scores, avg_score

if __name__ == '__main__':
    # train()
    test()