import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from collections import namedtuple, deque
import random
import numpy as np
import matplotlib.pyplot as plt
import os

from environment import Environment

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 网络结构
class QNet(nn.Module):
    def __init__(self, state_action, action_dim):
        super().__init__()
        self.fc1 = nn.Linear(state_action, 128)
        self.fc2 = nn.Linear(128, 128)
        self.fc3 = nn.Linear(128, action_dim)

        # 预设归一化参数
        self.register_buffer('max_days', torch.tensor(366.0))


    # 前向传播
    def forward(self, x):
        x_norm = x.clone()
        x_norm[:, 2] = x_norm[:, 2] / self.max_days

        x = F.relu(self.fc1(x_norm))
        x = F.relu(self.fc2(x))
        return self.fc3(x)
    
# 经验回放
Transition = namedtuple('Transition', ('state', 'action', 'reward', 'next_state', 'done'))

class ReplayBuffer:
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)    # 经验回放缓冲区

    # 交互push进来
    def push(self, *args):
        self.buffer.append(Transition(*args))

    # 随机采集一个batch
    def sample(self, batch_size):
        transitions = random.sample(self.buffer, batch_size)    # 随机采样
        batch = Transition(*zip(*transitions))    # 拆包
        states = torch.FloatTensor(batch.state)
        actions = torch.LongTensor(batch.action)
        rewards = torch.FloatTensor(batch.reward)
        next_states = torch.FloatTensor(batch.next_state)
        dones = torch.BoolTensor(batch.done)
        return states, actions, rewards, next_states, dones
    
    def __len__(self):
        return len(self.buffer)

# Agent
class DQNAgent:
    def __init__(self, state_dim, action_dim, learning_rate, gamma, buffer_size, batch_size, target_sync):
        # 网络
        self.q_net = QNet(state_dim, action_dim).to(device)
        self.target_net = QNet(state_dim, action_dim).to(device)
        # 同步主网络和影子网络
        self.target_net.load_state_dict(self.q_net.state_dict())
        self.target_net.eval()    # 影子网络只推理不求梯度

        # 优化器
        self.optimizer = optim.Adam(self.q_net.parameters(), lr=learning_rate)

        # 经验池
        self.buffer = ReplayBuffer(buffer_size)

        # 探索和训练计数
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.step_count = 0

        # 超参数
        self.action_dim = action_dim    
        self.gamma = gamma    # 折扣因子
        self.batch_size = batch_size
        self.target_sync = target_sync    # 目标网络同步频率
        
        # 添加价值函数记录
        self.q_values_history = []


    def act(self, state):
        # ε-贪婪策略
        if random.random() < self.epsilon:
            return np.random.randint(self.action_dim)
        
        # 贪婪动作
        state = torch.FloatTensor(state).unsqueeze(0).to(device)    # unsqueeze(0)增加一个维度
        with torch.no_grad():
            q_values = self.q_net(state)    # 计算Q值
        return q_values.argmax().item()    # 返回动作索引
        
    def remember(self, *args):
        self.buffer.push(*args)

    def update(self):
        # 经验回放
        if len(self.buffer) < self.batch_size:
            return
        
        # 采样经验
        states, actions, rewards, next_states, dones = self.buffer.sample(self.batch_size)
        states = states.float().to(device)
        actions = actions.long().to(device)
        rewards = rewards.float().to(device)
        next_states = next_states.float().to(device)
        dones = dones.bool().to(device)

        # 当前Q值
        current_q = self.q_net(states).gather(1, actions.unsqueeze(1))

        # 目标Q值
        with torch.no_grad():
            next_q = self.target_net(next_states).max(1)[0]
            target_q = rewards + (self.gamma * next_q * (~dones))    # ~dones:布尔取反

        # 计算损失和更新
        loss = F.mse_loss(current_q.squeeze(), target_q)
        
        # 更新网络
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        # 更新ε
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        # 同步目标网络
        self.step_count += 1
        if self.step_count % self.target_sync == 0:
            self.target_net.load_state_dict(self.q_net.state_dict())

        return loss.item()

    def _obs_to_state(self, obs):
        # 将观测转换为状态向量，包含历史销量信息
        state = [
            obs['inventory_ratio'],      # 已除以安全库存
            obs['transit'],             # 已除以安全库存  
            obs['day'],
            obs['is_order_day']
        ]
        
        # 添加前15天的销量数据（已经除以安全库存）
        if 'historical_sales' in obs:
            state.extend(obs['historical_sales'])
        
        return state
     


    def train(self, env, config):
        print("开始训练...")
        
        # 训练记录
        score = []
        eval_score = []
        losses = []
        best_score = -float('inf')
        
        # 创建一个固定的参考状态用于记录Q值变化
        reference_state = np.array([1.0, 0.5, 50, 1] + [0.1] * 15, dtype=np.float32)    # 19维状态
        
        for episode in range(config.train_episodes):
            state, _ = env.reset(config.mode)
            total_reward = 0
            done = False
            episode_losses = []

            while not done:
                # 将观测转换为状态向量
                state_vector = self._obs_to_state(state)
                action = self.act(state_vector)
                next_state, reward, done, _ = env.step(action, config.mode)

                next_state_vector = self._obs_to_state(next_state)
                self.remember(state_vector, action, reward, next_state_vector, done)
                loss = self.update()
                if loss is not None:
                    episode_losses.append(loss)

                state = next_state
                total_reward += reward

            print(f"Episode {episode + 1} : Reward = {total_reward}")
            losses.append(np.mean(episode_losses) if episode_losses else 0)
            score.append(total_reward)
            
            # 每5个episode评估一次
            if (episode + 1) % 5 == 0:
                # 记录Q值
                with torch.no_grad():
                    state_tensor = torch.FloatTensor(reference_state).unsqueeze(0).to(device)
                    q_vals = self.q_net(state_tensor)
                    max_q_value = q_vals.max().item()
                    self.q_values_history.append(max_q_value)
                
                avg_eval_score = self.evaluate(env, config, episodes=10)
                eval_score.append(avg_eval_score)
                print(f"\nEpisode {episode + 1} : Eval Score = {avg_eval_score}\n")

                # 保存最佳模型
                if avg_eval_score > best_score:
                    best_score = avg_eval_score
                    self.save_model(config.model_filename)
                    print('保存模型\n')

        print('训练完成\n')
        
        # 绘制训练曲线
        self.plot(score, losses)
        
        return score, eval_score, losses

    def evaluate(self, env, config, episodes=10):
        # 保存当前epsilon和训练状态
        eval_epsilon = self.epsilon
        self.epsilon = 0.0  # 纯贪婪策略
        self.q_net.eval()
        
        eval_scores = []
        for eval_episode in range(episodes): 
            eval_state, _ = env.reset(config.mode)
            eval_reward = 0
            eval_done = False
            
            while not eval_done:
                with torch.no_grad():
                    state_vector = self._obs_to_state(eval_state)
                    eval_action = self.act(state_vector)
                eval_state, reward, eval_done, _ = env.step(eval_action, config.mode)
                eval_reward += reward
            
            eval_scores.append(eval_reward)
        
        # 恢复训练状态
        self.epsilon = eval_epsilon
        self.q_net.train()
        
        return np.mean(eval_scores)

    def save_model(self, model_path):
        # 确保目录存在
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        torch.save(self.q_net.state_dict(), model_path)

    def load_model(self, model_path):
        self.q_net.load_state_dict(torch.load(model_path))

    def test(self, env, config, episodes=10):
        print('开始测试...')
        
        # 设置为纯贪婪策略
        self.epsilon = 0.0
        self.q_net.eval()
        
        test_scores = []
        
        for test_episode in range(episodes):
            test_state, _ = env.reset(config.mode)
            test_reward = 0
            test_done = False
            inventory_history = []

            while not test_done:
                state_vector = self._obs_to_state(test_state)
                test_action = self.act(state_vector)
                test_state, reward, test_done, _ = env.step(test_action, config.mode)
                test_reward += reward
                
                # 记录库存历史 - 通过库存比率计算实际库存
                current_inventory = test_state['inventory_ratio'] * env.SafetyStock
                inventory_history.append(current_inventory)

            test_scores.append(test_reward)
            print(f"Test Episode {test_episode + 1} : Score = {test_reward}")
            
            # 画库存曲线图
            self._plot_inventory(inventory_history, env.SafetyStock, test_episode + 1, env, config)

        avg_score = np.mean(test_scores)
        print(f"\nAverage Test Score = {avg_score}")
        print('测试完成\n')
        
        return test_scores, avg_score
    
    def _plot_inventory(self, inventory_history, safety_stock, episode_num, env, config):
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        
        # 创建双y轴
        fig, ax1 = plt.subplots(figsize=(15, 8))
        ax2 = ax1.twinx()
        
        # 获取真实日期和销量数据
        dates, sales_data = self._get_real_dates_and_sales(env, config)
        
        if dates is not None and len(dates) >= len(inventory_history):
            x_axis = dates[:len(inventory_history)]
            sales_for_plot = sales_data[:len(inventory_history)]
        else:
            # 如果没有真实日期，使用相对日期
            import pandas as pd
            start_date = pd.Timestamp('2024-01-01')
            x_axis = pd.date_range(start=start_date, periods=len(inventory_history), freq='D')
            sales_for_plot = [100] * len(inventory_history)  # 默认销量
        
        # 画销量柱状图（右y轴）
        bars = ax2.bar(x_axis, sales_for_plot, alpha=0.6, color='#FF6B35', width=0.8, label='每日销量')
        
        # 画库存曲线（左y轴）
        line1 = ax1.plot(x_axis, inventory_history, 'b-', linewidth=2, label='库存水平')
        
        # 画安全库存红线（左y轴）
        line2 = ax1.axhline(y=safety_stock, color='r', linestyle='--', linewidth=2, label=f'安全库存 ({safety_stock})')
        
        # 设置左y轴（库存）
        ax1.set_xlabel('日期')
        ax1.set_ylabel('库存数量', color='b')
        ax1.tick_params(axis='y', labelcolor='b')
        ax1.set_ylim(bottom=0)
        ax1.grid(True, alpha=0.3)
        
        # 设置右y轴（销量）
        ax2.set_ylabel('销量', color='orange')
        ax2.tick_params(axis='y', labelcolor='orange')
        ax2.set_ylim(bottom=0)
        
        # 设置x轴日期格式
        import matplotlib.dates as mdates
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))  # 每两周显示一次
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 合并图例
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.title(f'测试Episode {episode_num} - 库存变化与销量对比')
        plt.tight_layout()
        plt.show()
    
    def _get_real_dates_and_sales(self, env, config):
        """获取当前模式对应的真实日期和销量数据"""
        mode = config.mode
        
        if mode == 'B0B41X9SMT' and hasattr(env, 'real_dates_B0B41X9SMT'):
            # 获取当前episode使用的数据起始位置
            if hasattr(env, 'real_data_start_B0B41X9SMT'):
                start_idx = env.real_data_start_B0B41X9SMT
                dates = env.real_dates_B0B41X9SMT[start_idx:]
                sales = env.real_sales_data_B0B41X9SMT[start_idx:]
                return dates, sales
        elif mode == 'B0BG1H99FB' and hasattr(env, 'real_dates_B0BG1H99FB'):
            # 获取当前episode使用的数据起始位置
            if hasattr(env, 'real_data_start_B0BG1H99FB'):
                start_idx = env.real_data_start_B0BG1H99FB
                dates = env.real_dates_B0BG1H99FB[start_idx:]
                sales = env.real_sales_data_B0BG1H99FB[start_idx:]
                return dates, sales
        
        return None, None

    def plot(self, scores, losses):
        plt.figure(figsize=(18, 5))
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        
        plt.subplot(1, 3, 1)
        plt.plot(scores)
        plt.title('Training Reward')
        plt.xlabel('Episode')
        plt.ylabel('Reward')

        plt.subplot(1, 3, 2)
        plt.plot(losses)
        plt.title('Training Loss')
        plt.xlabel('Episode')
        plt.ylabel('Loss')
        
        plt.subplot(1, 3, 3)
        plt.plot(self.q_values_history)
        plt.title('Value Function (Max Q-value)')
        plt.xlabel('Episode (x10)')
        plt.ylabel('Max Q-value')

        plt.savefig('training.png')

        plt.tight_layout()
        plt.show()