# 实验记录

## 实验1 - 基础配置
**时间**: 2025-01-27  
**模式**: linear  

### 网络架构
- 输入维度: 4 (inventory_ratio, transit, day, is_order_day)
- 隐藏层: [128, 128]
- 输出维度: 31 (动作空间)
- 激活函数: ReLU
- 归一化: day/366

### 训练参数
- learning_rate: 0.0003
- gamma: 0.95
- buffer_size: 25000
- batch_size: 32
- target_sync: 100
- train_episodes: 500
- test_episodes: 5

### 探索策略
- epsilon_start: 1.0
- epsilon_min: 0.01
- epsilon_decay: 0.995

### 环境参数
- ReplenishmentCycle: 10
- LeadTime: 15
- SafetyStock: 3000
- InitialInventory: random(3000-3600)
- sale_base: 100
- 动作空间: [0, 150, 300, ..., 4500] (31个选项)

### 奖励函数
```
if inventory <= 0: -5 * (SafetyStock - inventory) / SafetyStock
elif inventory < SafetyStock: -(SafetyStock - inventory) / SafetyStock  
elif inventory > SafetyStock: -(inventory - SafetyStock) / SafetyStock
else: 0
```

### 结果
- 最佳模型测试分数: -32.85
- 最终模型测试分数: -32.63
- 备注: 两个模型测试结果相近，分数稳定但偏低 

---

## 实验2 - 修改后配置
**时间**: 2025-01-27  
**模式**: linear  

### 网络架构
- 输入维度: 4 (inventory_ratio, transit, day, is_order_day)
- 隐藏层: [128, 128]
- 输出维度: 31 (动作空间)
- 激活函数: ReLU
- 归一化: day/366

### 训练参数
- learning_rate: 0.0003
- gamma: 0.95
- buffer_size: 25000
- batch_size: 32
- target_sync: 100
- train_episodes: 500
- test_episodes: 5

### 探索策略
- epsilon_start: 1.0
- epsilon_min: 0.01
- epsilon_decay: 0.995

### 环境参数
- ReplenishmentCycle: 10
- LeadTime: 15
- SafetyStock: 3000
- InitialInventory: random(3000-3600)
- sale_base: 100
- 动作空间: [0, 150, 300, ..., 4500] (31个选项)

### 奖励函数
```
if inventory <= 0: -5 * (SafetyStock - inventory) / SafetyStock
elif inventory < SafetyStock: -(SafetyStock - inventory) / SafetyStock  
elif inventory > SafetyStock: -(inventory - SafetyStock) / SafetyStock
else: 0
```

### 结果
- 最佳模型测试分数: -22.85
- 最终模型测试分数: -66.21
- 备注: 最佳模型表现提升，但最终模型表现下降明显，可能存在过拟合

---

## 实验3 - 当前配置
**时间**: 2025-01-27  
**模式**: linear  

### 网络架构
- 输入维度: 4 (inventory_ratio, transit, day, is_order_day)
- 隐藏层: [128, 128]
- 输出维度: 31 (动作空间)
- 激活函数: ReLU
- 归一化: day/366

### 训练参数
- learning_rate: 0.0003
- gamma: 0.95
- buffer_size: 25000
- batch_size: 32
- target_sync: 100
- train_episodes: 500
- test_episodes: 5

### 探索策略
- epsilon_start: 1.0
- epsilon_min: 0.01
- epsilon_decay: 0.995

### 环境参数
- ReplenishmentCycle: 10
- LeadTime: 15
- SafetyStock: 3000
- InitialInventory: random(3000-3600)
- sale_base: 100
- 动作空间: [0, 150, 300, ..., 4500] (31个选项)

### 奖励函数
```
if inventory <= 0: -5 * (SafetyStock - inventory) / SafetyStock
elif inventory < SafetyStock: -(SafetyStock - inventory) / SafetyStock  
elif inventory > SafetyStock: -(inventory - SafetyStock) / SafetyStock
else: 0
```

### 结果
- 最佳模型测试分数: -36.70
- 最终模型测试分数: -126.81
- 备注: 最佳模型表现回落，最终模型表现大幅恶化，训练不稳定

---

## 实验4 - 当前配置
**时间**: 2025-01-27  
**模式**: linear  

### 网络架构
- 输入维度: 4 (inventory_ratio, transit, day, is_order_day)
- 隐藏层: [128, 128]
- 输出维度: 31 (动作空间)
- 激活函数: ReLU
- 归一化: day/366

### 训练参数
- learning_rate: 0.0003
- gamma: 0.95
- buffer_size: 25000
- batch_size: 32
- target_sync: 100
- train_episodes: 500
- test_episodes: 5

### 探索策略
- epsilon_start: 1.0
- epsilon_min: 0.01
- epsilon_decay: 0.995

### 环境参数
- ReplenishmentCycle: 10
- LeadTime: 15
- SafetyStock: 3000
- InitialInventory: random(3000-3600)
- sale_base: 100
- 动作空间: [0, 150, 300, ..., 4500] (31个选项)

### 奖励函数
```
if inventory <= 0: -5 * (SafetyStock - inventory) / SafetyStock
elif inventory < SafetyStock: -(SafetyStock - inventory) / SafetyStock  
elif inventory > SafetyStock: -(inventory - SafetyStock) / SafetyStock
else: 0
```

### 结果
- 最佳模型测试分数: -24.32
- 最终模型测试分数: -1414.16
- 备注: 最佳模型达到目前最好表现，但最终模型崩溃式恶化，训练极不稳定

---

## 实验模板 (复制使用)
**时间**: YYYY-MM-DD  
**模式**: linear/sin/poisson  

### 网络架构
- 输入维度: 
- 隐藏层: 
- 输出维度: 
- 激活函数: 
- 归一化: 

### 训练参数
- learning_rate: 
- gamma: 
- buffer_size: 
- batch_size: 
- target_sync: 
- train_episodes: 
- test_episodes: 

### 探索策略
- epsilon_start: 
- epsilon_min: 
- epsilon_decay: 

### 环境参数
- ReplenishmentCycle: 
- LeadTime: 
- SafetyStock: 
- InitialInventory: 
- sale_base: 
- 动作空间: 

### 奖励函数
```
修改内容
```

### 结果
- 最佳评估分数: 
- 最终测试分数: 
- 训练完成时间: 
- 备注: 

---