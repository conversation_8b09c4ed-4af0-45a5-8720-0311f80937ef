import numpy as np
import pandas as pd
import math

class Environment:
    def __init__(self):
        self.ReplenishmentCycle = 5                                                           # 补货周期
        self.LeadTime = 70                                                                      # 到货周期基准值
        self.base_safety_stock = 9000                                                          # 基准安全库存
        self.safety_stock_range = (0.8, 1.2)                                                  # 波动范围（80%-120%）
        self.SafetyStock = self.base_safety_stock                                              # 当前episode的安全库存
        self.InitialInventory = np.random.randint(self.SafetyStock, int(self.SafetyStock * 1.2))    # 初始库存
        self.sale = 100                                                                        # 销量基准
        
        self.record_columns = [
            'day',
            'open_inv',
            'sale',
            'arrive',
            'order',
            'state',
            'transit',
            'close_inv'
        ]
        self.log = pd.DataFrame(columns=self.record_columns)

        # 动作空间
        self.action_space = {
            'n': 31,    # 动作数量
            'options': [round(0.05 * i * self.SafetyStock) for i in range(31)]
        }

        self.pregenerated_sales = []
        self.sin_phase = 0  # 正弦波相位，在每个episode开始时重新生成
        
        # 预加载真实销量数据
        self.real_sales_data_B0B41X9SMT = None
        self.real_sales_data_B0BG1H99FB = None
        self.load_real_data()

    def load_real_data(self):
        # 使用相对路径，从当前文件所在目录向上一级查找Excel文件
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        
        # 加载B0B41X9SMT数据
        b0b41x9smt_path = os.path.join(parent_dir, 'B0B41X9SMT.xlsx')
        if os.path.exists(b0b41x9smt_path):
            df1 = pd.read_excel(b0b41x9smt_path)
            self.real_sales_data_B0B41X9SMT = df1['销量'].values
            self.real_dates_B0B41X9SMT = pd.to_datetime(df1['日期']).values
        else:
            print(f"警告: 找不到文件 {b0b41x9smt_path}")
            self.real_sales_data_B0B41X9SMT = None
            
        # 尝试加载B0BG1H99FB数据，如果不存在则使用其他可用的Excel文件
        b0bg1h99fb_path = os.path.join(parent_dir, 'B0BG1H99FB.xlsx')
        if os.path.exists(b0bg1h99fb_path):
            df2 = pd.read_excel(b0bg1h99fb_path)
            self.real_sales_data_B0BG1H99FB = df2['销量'].values
            self.real_dates_B0BG1H99FB = pd.to_datetime(df2['日期']).values
        else:
            # 如果B0BG1H99FB.xlsx不存在，尝试使用其他Excel文件
            alternative_files = ['B0BF9N3681.xlsx', 'B0CQYBG2T4.xlsx', 'B0CQYBQ9YD.xlsx']
            for alt_file in alternative_files:
                alt_path = os.path.join(parent_dir, alt_file)
                if os.path.exists(alt_path):
                    print(f"使用替代文件: {alt_file}")
                    df2 = pd.read_excel(alt_path)
                    self.real_sales_data_B0BG1H99FB = df2['销量'].values
                    self.real_dates_B0BG1H99FB = pd.to_datetime(df2['日期']).values
                    break
            else:
                print("警告: 找不到任何可用的B0BG1H99FB替代文件")
         
         
    def linear(self, x, b):
        return b

    def sin(self, x , A , w , c):
        return A * np.sin(w * x + self.sin_phase) + c

    def poisson(self, x, lam):
        return np.random.poisson(lam)
    
    def B0B41X9SMT(self, day):
        if self.real_sales_data_B0B41X9SMT is None:
            return self.sale
        
        required_length = 366 + self.LeadTime + 15
        if not hasattr(self, 'real_data_start_B0B41X9SMT'):
            max_start = len(self.real_sales_data_B0B41X9SMT) - required_length
            self.real_data_start_B0B41X9SMT = np.random.randint(0, max_start + 1)
    
        return self.real_sales_data_B0B41X9SMT[self.real_data_start_B0B41X9SMT + day]

    def B0BG1H99FB(self, day):
        if self.real_sales_data_B0BG1H99FB is None:
            return self.sale
        
        required_length = 366 + self.LeadTime + 15
        if not hasattr(self, 'real_data_start_B0BG1H99FB'):
            max_start = len(self.real_sales_data_B0BG1H99FB) - required_length
            self.real_data_start_B0BG1H99FB = np.random.randint(0, max_start + 1)
    
        return self.real_sales_data_B0BG1H99FB[self.real_data_start_B0BG1H99FB + day]

    # 预生成整个episode的销量数据 
    def pregenerate_sales(self, mode):
        length = 366 + self.LeadTime + 15  # 增加15天用于历史观测
        sales_list = []
        
        for day in range(-15, length - 15):  # 从-15天开始生成
            if mode == 'linear':
                sale = self.linear(day, self.sale)
            elif mode == 'sin':
                sale = self.sin(day, self.sale / 2, 0.1, self.sale)
            elif mode == 'poisson':
                sale = self.poisson(day, self.sale)
            elif mode == 'B0B41X9SMT':
                sale = self.B0B41X9SMT(day + 15)  # 调整索引
            elif mode == 'B0BG1H99FB':
                sale = self.B0BG1H99FB(day + 15)  # 调整索引
            else:
                sale = self.sale  # 默认销量
             
            sales_list.append(sale)
        
        return sales_list


    def record(self, day, mode, action):
        open_inv = self.log.loc[day-1, 'close_inv'] if day > 0 else self.InitialInventory
        arrive = self.log.loc[day - self.LeadTime, 'order'] if day >= self.LeadTime else 0
        
        sale = self.pregenerated_sales[day + 15] if day + 15 < len(self.pregenerated_sales) else 0
        
        close_inv = open_inv + arrive - sale
        state = 1 if (day - 1) % self.ReplenishmentCycle == 0 else 0

        # 在途 = 上一天在途 + 今天订单 - 今天到货
        if day == 0:
            transit = action  # 第一天的订单
        else:
            prev_transit = self.log.loc[day-1, 'transit'] if day-1 in self.log['day'].values else 0
            transit = prev_transit + action - arrive

        new_record = {
            'day': day,
            'open_inv': open_inv,
            'sale': sale,
            'arrive': arrive,
            'order': action,
            'state': state,
            'transit': transit,
            'close_inv': close_inv
        }
        
        # 更新日志
        if day in self.log['day'].values:
            self.log.loc[self.log['day'] == day] = new_record
        else:
            self.log = pd.concat([self.log, pd.DataFrame([new_record])], ignore_index=True)
        
        return new_record
    
    def reset(self, mode):
        # 使用固定的基准安全库存
        self.SafetyStock = self.base_safety_stock

        # 清理两个真实数据模式的起始位置
        if hasattr(self, 'real_data_start_B0B41X9SMT'):
            delattr(self, 'real_data_start_B0B41X9SMT')
        if hasattr(self, 'real_data_start_B0BG1H99FB'):
            delattr(self, 'real_data_start_B0BG1H99FB')
        
        # 基于新的安全库存重新计算初始库存
        self.InitialInventory = np.random.randint(self.SafetyStock, int(self.SafetyStock * 1.2) + 1)
        
        # 重新计算动作空间
        self.action_space['options'] = [round(0.05 * i * self.SafetyStock) for i in range(31)]
        
        self.current_inventory = self.InitialInventory
        self.log = pd.DataFrame(columns=self.record_columns)
        
        # 为sin模式生成随机相位
        self.sin_phase = np.random.uniform(0, 2 * math.pi)
        
        # 只设置第0天的初始记录
        self.pregenerated_sales = self.pregenerate_sales(mode)
        sale = self.pregenerated_sales[15]  # 第0天对应索引15
        
        initial_record = {
            'day': 0,
            'open_inv': self.InitialInventory,
            'sale': sale,
            'arrive': 0,
            'order': 0,
            'transit': 0,  
            'state': 0,    
            'close_inv': self.InitialInventory - sale
        }
        
        self.log = pd.DataFrame([initial_record])
        
        # 构建初始观测值，包含前15天销量（除以安全库存进行归一化）
        historical_sales = []
        for i in range(15):
            sales_day = 0 - 14 + i  # 从第-14天到第0天
            historical_sales.append(self.pregenerated_sales[sales_day + 15] / self.SafetyStock)
        
        initial_observation = {
            'inventory_ratio': initial_record['close_inv'] / self.SafetyStock,
            'transit': initial_record['transit'] / self.SafetyStock,
            'day': 0,
            'is_order_day': initial_record['state'],
            'historical_sales': historical_sales  # 新增：前15天销量（已除以安全库存）
        }
        
        return initial_observation, {}
    
    # 获取指定天数的开盘库存
    def get_future_inventory(self, day):
        if day == 0:
            return self.InitialInventory
        elif day <= len(self.log) - 1:
            # 如果该天已经记录过，直接从log获取
            return self.log.loc[self.log['day'] == day, 'open_inv'].iloc[0]
        else:
            # 需要模拟计算到该天的库存
            last_day = len(self.log) - 1
            last_close_inv = self.log.loc[self.log['day'] == last_day, 'close_inv'].iloc[0]
            
            # 从last_day+1模拟到day-1
            current_inv = last_close_inv
            for sim_day in range(last_day + 1, day):
                arrive = self.get_future_arrival(sim_day)
                sale = self.pregenerated_sales[sim_day + 15] if sim_day + 15 < len(self.pregenerated_sales) else 0
                current_inv = current_inv + arrive - sale
            
            return current_inv

    # 获取指定天数的到货量
    def get_future_arrival(self, day):
        order_day = day - self.LeadTime
        if order_day < 0:
            return 0
        elif order_day <= len(self.log) - 1:
            # 从已记录的log中获取订单量
            return self.log.loc[self.log['day'] == order_day, 'order'].iloc[0]
        else:
            # 超出已记录范围，订单量为0（因为agent只在366天内操作）
            return 0

    # 奖励计算函数
    def calculate_reward(self, inventory):
        if inventory <= 0:
            return -5 * (self.SafetyStock - inventory) / self.SafetyStock
        elif inventory < self.SafetyStock:
            return -(self.SafetyStock - inventory) / self.SafetyStock
        elif inventory > self.SafetyStock:
            return -(inventory - self.SafetyStock) / self.SafetyStock
        else:
            return 0
        
        
    def step(self, action, mode):
        current_day = len(self.log)

        if current_day > 0 and (current_day - 1) % self.ReplenishmentCycle == 0:
            action = self.action_space['options'][action]
        else:
            action = 0

        record = self.record(current_day, mode, action)

        # 获取前15天的销量数据（除以安全库存）
        historical_sales = []
        for i in range(15):
            sales_day = current_day - 14 + i  # 从current_day-14到current_day
            historical_sales.append(self.pregenerated_sales[sales_day + 15] / self.SafetyStock)
        
        # 构建观测值
        observation = {
            'inventory_ratio': record['close_inv'] / self.SafetyStock,
            'transit': record['transit'] / self.SafetyStock,
            'day': current_day,
            'is_order_day': record['state'],
            'historical_sales': historical_sales  # 新增：前15天销量（已除以安全库存）
        }

        # 计算奖励
        reward_day = current_day + self.LeadTime
        
        if reward_day + 15 < len(self.pregenerated_sales):
            future_open_inv = self.get_future_inventory(reward_day)
            future_arrive = self.get_future_arrival(reward_day) 
            future_sale = self.pregenerated_sales[reward_day + 15]
            future_close_inv = future_open_inv + future_arrive - future_sale
            
            # 基于未来库存状态计算奖励
            reward = self.calculate_reward(future_close_inv)
        else:
            # 超出预测范围，使用当前库存计算奖励
            reward = self.calculate_reward(record['close_inv'])

        done = current_day >= 366

        return observation, reward, done, {}