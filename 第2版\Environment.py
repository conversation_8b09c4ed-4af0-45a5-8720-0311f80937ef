import random
import numpy as np
import pandas as pd

class Env:
    def __init__(self):
        self.ReplenishmentCycle = 10
        self.BeforeTime = 30
        self.LeadTime = 70
        self.Time = 210

        self.SafetyStock = 10000

        self.RecordColums = [
            'Date',
            'OpenInv',
            'Sale',
            'StockUp',
            'ArriveTime',
            'Arrive',
            'State',
            'Transfit',
            'AdvertisingSpeed',
            'Comment',
            'MainCategoryBSR',
            'SubCategoryBSR',
            'CloseInv'
        ]
        self.log = pd.DataFrame(columns=self.RecordColums)
        
        # 动作空间
        self.ActionSapce = {
            'n': 31,
            'options': [round(0.05 * i * self.SafetyStock) for i in range(31)]
        }

        self.FilePath = [
            'D:\备货控制\B0B41X9SMT.xlsx',
            'D:\备货控制\B0BF9N3681.xlsx',
            'D:\备货控制\B0CQYBG2T4.xlsx',
            'D:\备货控制\B0CQYBQ9YD.xlsx'
        ]

        self.dfs = []
        for i in range(len(self.FilePath)):
            self.dfs.append(self.load(self.FilePath[i]))

        def load(self, path):
            df = pd.read_excel(path)
            title_list = [
                '真实库存','销量','广告花费','评论数','大类排名','小类排名1_排名'
            ]
            first_stock_idx = df[df[title_list[0]] > 0].index.min()
            last_stock_idx = df[df[title_list[0]] > 0].index.max()

            if pd.isna(first_stock_idx) or pd.isna(last_stock_idx):
                return pd.DataFrame(columns=title_list)
            time_df = df.loc[first_stock_idx:last_stock_idx].copy()
            result_df = time_df[title_list].copy()
            
            return result_df

        def split(self, dfs, mode):
            if mode == 'test':
                return dfs[0].tail(320)
            elif mode == 'train':
                sources = []
                sources.append(dfs[0].lioc[:-320])
                for df in dfs[1:]:
                    if len(df)>=320: sources.append(df)
            selected_df = random.choice(sources)
            start = random.randint(0, len(selected_df)-320)
            return selected_df.iloc[start:start+320].values.tolist()

        def record(self, action, dataset):
            current_day_idx = self.log[self.log['Date'].isna()].index[0]
            current_day = current_day_idx 
            
            if pd.isna(self.log.loc[current_day_idx, 'OpenInv']):
                self.log.loc[current_day_idx, 'OpenInv'] = self.log.loc[current_day_idx-1, 'CloseInv']
            
            if self.log.loc[current_day_idx, 'State'] == 1:
                self.log.loc[current_day_idx, 'StockUp'] = action
            else:
                self.log.loc[current_day_idx, 'StockUp'] = 0
            
            arrive_amount = self.log[self.log['ArriveTime'] == current_day]['StockUp'].sum()
            self.log.loc[current_day_idx, 'Arrive'] = arrive_amount
            
            if pd.isna(self.log.loc[current_day_idx, 'CloseInv']):
                open_inv = self.log.loc[current_day_idx, 'OpenInv']
                sale = self.log.loc[current_day_idx, 'Sale']
                arrive = self.log.loc[current_day_idx, 'Arrive']
                self.log.loc[current_day_idx, 'CloseInv'] = open_inv - sale + arrive
            
            in_transit = self.log[(self.log['StockUp'] > 0) & (self.log['ArriveTime'] > current_day)]['StockUp'].sum()
            self.log.loc[current_day_idx, 'Transfit'] = in_transit
                    
     

        def reset(self, dataset):
            self.log = pd.DataFrame(columns=self.RecordColums)

            for day in range(320):
                if day < 30:
                    date = day
                else:
                    date = None

                if day >= 30 and day < 240 and (day - 30) % 10 == 1:
                    state = 1
                else:
                    state = 0

                sale = dataset[day][1]
                advertising_speed = dataset[day][2]
                comment = dataset[day][3]
                main_category_bsr = dataset[day][4]
                sub_category_bsr = dataset[day][5]

                if state == 1:
                    arrive_time = day + random.randint(int(self.LeadTime * 0.9), int(self.LeadTime * 1.1))
                else:
                    arrive_time = None

                if day <= 90:
                    open_inv = dataset[day][0]
                    close_inv = dataset[day+1][0]
                else:
                    open_inv = None
                    close_inv = None

                # 第30天的初始值
                if day == 30:
                    stock_up = 0
                    arrive = 0
                    transfit = 0
                else:
                    stock_up = None
                    arrive = None
                    transfit = None

                row_data = {
                    'Date': day,
                    'OpenInv': open_inv if 'open_inv' in locals() else None,
                    'Sale': sale,
                    'StockUp': stock_up,
                    'ArriveTime': arrive_time,
                    'Arrive': arrive,
                    'State': state,
                    'Transfit': transfit,
                    'AdvertisingSpeed': advertising_speed,
                    'Comment': comment,
                    'MainCategoryBSR': main_category_bsr,
                    'SubCategoryBSR': sub_category_bsr,
                    'CloseInv': close_inv if 'close_inv' in locals() else None
                }

                self.log = pd.concat([self.log, pd.DataFrame([row_data])], ignore_index=True)