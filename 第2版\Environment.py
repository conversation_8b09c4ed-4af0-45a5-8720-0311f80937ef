import random
import numpy as np
import pandas as pd

class Env:
    def __init__(self):
        self.ReplenishmentCycle = 10
        self.BeforeTime = 30
        self.LeadTime = 70
        self.Time = 210

        self.SafetyStock = 10000

        self.RecordColums = [
            'Date',
            'OpenInv',
            'Sale',
            'StockUp',
            'ArriveTime',
            'Arrive',
            'State',
            'Transfit',
            'AdvertisingSpeed',
            'Comment',
            'MainCategoryBSR',
            'SubCategoryBSR',
            'CloseInv'
        ]
        self.log = pd.DataFrame(columns=self.RecordColums)
        
        # 动作空间
        self.ActionSapce = {
            'n': 31,
            'options': [round(0.05 * i * self.SafetyStock) for i in range(31)]
        }

        self.FilePath = [
            'D:\备货控制\B0B41X9SMT.xlsx',
            'D:\备货控制\B0BF9N3681.xlsx',
            'D:\备货控制\B0CQYBG2T4.xlsx',
            'D:\备货控制\B0CQYBQ9YD.xlsx'
        ]

        self.dfs = []
        for i in range(len(self.FilePath)):
            self.dfs.append(self.load(self.FilePath[i]))

        def load(self, path):
            df = pd.read_excel(path)
            title_list = [
                '真实库存','销量','广告花费','评论数','大类排名','小类排名1_排名'
            ]
            first_stock_idx = df[df[title_list[0]] > 0].index.min()
            last_stock_idx = df[df[title_list[0]] > 0].index.max()

            if pd.isna(first_stock_idx) or pd.isna(last_stock_idx):
                return pd.DataFrame(columns=title_list)
            time_df = df.loc[first_stock_idx:last_stock_idx].copy()
            result_df = time_df[title_list].copy()
            
            return result_df

        def split(self, dfs, mode):
            if mode == 'test':
                return dfs[0].tail(320)
            elif mode == 'train':
                sources = []
                sources.append(dfs[0].lioc[:-320])
                for df in dfs[1:]:
                    if len(df)>=320: sources.append(df)
            selected_df = random.choice(sources)
            start = random.randint(0, len(selected_df)-320)
            return selected_df.iloc[start:start+320].values.tolist()

        def record(self, date, action, dataset):
            

        def reset(self, dataset):
            self.log = pd.DataFrame(columns=self.RecordColums)
            

