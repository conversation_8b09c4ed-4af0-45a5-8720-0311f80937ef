import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import ParameterGrid
import matplotlib.pyplot as plt
import warnings
import os
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 创建model文件夹
os.makedirs('model', exist_ok=True)

def prepare_data(df):
    required_columns = ['销量', '广告花费', '评论数', '大类排名', '小类排名1_排名', '真实库存']
    missing_cols = [col for col in required_columns if col not in df.columns]
    if missing_cols:
        print(f"缺失列: {missing_cols}")
        return None
    
    # 根据真实库存截取数据范围
    inventory = df['真实库存'].values
    
    # 找到第一个真实库存不为0的位置
    non_zero_indices = np.where(inventory != 0)[0]
    if len(non_zero_indices) == 0:
        print("警告: 真实库存全为0，使用全部数据")
        start_idx = 0
        end_idx = len(df)
    else:
        start_idx = non_zero_indices[0]
        
        # 找到最后一个真实库存为0的位置（在有库存之后）
        after_start = inventory[start_idx:]
        zero_indices_after_start = np.where(after_start == 0)[0]
        
        if len(zero_indices_after_start) == 0:
            # 没有找到库存为0的情况，使用到最后
            end_idx = len(df)
            print(f"数据截取: 从第{start_idx+1}行开始到最后 (真实库存始终>0)")
        else:
            # 找到最后一个为0的位置
            last_zero_idx = start_idx + zero_indices_after_start[-1]
            end_idx = last_zero_idx + 1  # 包含最后一个为0的位置
            print(f"数据截取: 从第{start_idx+1}行到第{end_idx}行 (真实库存有效范围)")
    
    # 截取数据
    df_filtered = df.iloc[start_idx:end_idx].copy()
    print(f"原始数据: {len(df)}行 → 截取后: {len(df_filtered)}行")
    
    # 提取需要的列（不包括真实库存，只用于截取）
    data = df_filtered[required_columns[:-1]].copy()  # 排除'真实库存'
    data = data.dropna()
    print(f"删除缺失值后: {len(data)}行")
    
    return data

class MultivariateLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, dropout=0.2):
        super(MultivariateLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=dropout)
        self.fc = nn.Linear(hidden_size, 1)  # 输出单个值
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        out, _ = self.lstm(x, (h0, c0))
        # 输出每个时间步的预测
        out = self.dropout(out)
        out = self.fc(out)  # (batch_size, seq_len, 1)
        return out.squeeze(-1)  # (batch_size, seq_len)

def split_data(data, test_days=70, val_days=70):
    total_len = len(data)
    test_start = total_len - test_days
    test_data = data.iloc[test_start:].copy()
    val_start = test_start - val_days
    val_data = data.iloc[val_start:test_start].copy()
    train_data = data.iloc[:val_start].copy()
    return train_data, val_data, test_data

def create_dataset(data):
    # 输入：整个序列的特征 (seq_len, features)
    # 输出：整个序列的销量 (seq_len,)
    X = data.iloc[:, 1:].values  # 特征：广告花费、评论数、大类排名、小类排名1_排名
    y = data.iloc[:, 0].values   # 目标：销量
    return X, y

def train_model(model, train_loader, val_loader, criterion, optimizer, epochs=100, patience=10):
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0
    
    for epoch in range(epochs):
        model.train()
        train_loss = 0
        for batch_X, batch_y in train_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            break
            
        if (epoch + 1) % 20 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')
    
    if 'best_model_state' in locals():
        model.load_state_dict(best_model_state)
    return train_losses, val_losses

def grid_search(train_data, val_data):
    param_grid = {
        'hidden_size': [32, 64, 128],
        'num_layers': [1, 2],
        'learning_rate': [0.001, 0.01],
        'dropout': [0.1, 0.2]
    }
    
    best_params = None
    best_val_loss = float('inf')
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()
    # 只用训练集拟合标准化器，避免测试集泄露
    train_features = train_data.iloc[:, 1:].values
    scaler_X.fit(train_features)
    train_target = train_data.iloc[:, 0].values.reshape(-1, 1)
    scaler_y.fit(train_target)
    
    train_scaled = train_data.copy()
    train_scaled.iloc[:, 1:] = scaler_X.transform(train_data.iloc[:, 1:].values)
    train_scaled.iloc[:, 0] = scaler_y.transform(train_data.iloc[:, 0].values.reshape(-1, 1)).flatten()
    
    val_scaled = val_data.copy()
    val_scaled.iloc[:, 1:] = scaler_X.transform(val_data.iloc[:, 1:].values)
    val_scaled.iloc[:, 0] = scaler_y.transform(val_data.iloc[:, 0].values.reshape(-1, 1)).flatten()
    
    print(f"开始网格搜索，共{len(list(ParameterGrid(param_grid)))}个参数组合...")
    
    for i, params in enumerate(ParameterGrid(param_grid)):
        print(f"[{i+1}/{len(list(ParameterGrid(param_grid)))}] 测试参数: {params}")
        try:
            X_train, y_train = create_dataset(train_scaled)
            X_val, y_val = create_dataset(val_scaled)
            
            if len(X_train) == 0 or len(X_val) == 0:
                print("数据集为空，跳过")
                continue
            
            # 转换为批次大小为1的张量
            X_train_tensor = torch.FloatTensor(X_train).unsqueeze(0)  # (1, seq_len, features)
            y_train_tensor = torch.FloatTensor(y_train).unsqueeze(0)  # (1, seq_len)
            X_val_tensor = torch.FloatTensor(X_val).unsqueeze(0)
            y_val_tensor = torch.FloatTensor(y_val).unsqueeze(0)
            
            train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            
            train_loader = DataLoader(train_dataset, batch_size=1, shuffle=False)
            val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
            
            input_size = X_train.shape[1]  # 特征数量
            model = MultivariateLSTM(
                input_size=input_size,
                hidden_size=params['hidden_size'],
                num_layers=params['num_layers'],
                dropout=params['dropout']
            ).to(device)
            
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=params['learning_rate'])
            
            _, val_losses = train_model(model, train_loader, val_loader, criterion, optimizer, epochs=50, patience=5)
            min_val_loss = min(val_losses)
            
            if min_val_loss < best_val_loss:
                best_val_loss = min_val_loss
                best_params = params
                print(f"找到更好的参数！验证损失: {min_val_loss:.4f}")
            else:
                print(f"验证损失: {min_val_loss:.4f}")
            
        except Exception as e:
            print(f"参数组合失败: {str(e)}")
            continue
    
    print(f"\n网格搜索完成！最佳参数: {best_params}")
    print(f"最佳验证损失: {best_val_loss:.4f}")
    return best_params, scaler_X, scaler_y

def main(excel_file_path):
    # 从文件名提取ASIN
    filename = os.path.basename(excel_file_path)
    asin = os.path.splitext(filename)[0]  # 去掉.xlsx扩展名
    print(f"正在处理ASIN: {asin}")
    print(f"文件路径: {excel_file_path}")
    
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file_path)
        print(f"成功读取文件，数据形状: {df.shape}")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    data = prepare_data(df)
    if data is None:
        return
    
    train_data, val_data, test_data = split_data(data)
    
    if len(train_data) < 20:
        return
    
    best_params, scaler_X, scaler_y = grid_search(train_data, val_data)
    
    if best_params is None:
        return
    
    # 使用训练集拟合的标准化器处理所有数据
    train_scaled = train_data.copy()
    train_scaled.iloc[:, 1:] = scaler_X.transform(train_data.iloc[:, 1:].values)
    train_scaled.iloc[:, 0] = scaler_y.transform(train_data.iloc[:, 0].values.reshape(-1, 1)).flatten()
    
    val_scaled = val_data.copy()
    val_scaled.iloc[:, 1:] = scaler_X.transform(val_data.iloc[:, 1:].values)
    val_scaled.iloc[:, 0] = scaler_y.transform(val_data.iloc[:, 0].values.reshape(-1, 1)).flatten()
    
    test_scaled = test_data.copy()
    test_scaled.iloc[:, 1:] = scaler_X.transform(test_data.iloc[:, 1:].values)
    test_scaled.iloc[:, 0] = scaler_y.transform(test_data.iloc[:, 0].values.reshape(-1, 1)).flatten()
    
    X_train, y_train = create_dataset(train_scaled)
    X_val, y_val = create_dataset(val_scaled)
    X_test, y_test = create_dataset(test_scaled)
    
    X_train_tensor = torch.FloatTensor(X_train).unsqueeze(0)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(0)
    X_val_tensor = torch.FloatTensor(X_val).unsqueeze(0)
    y_val_tensor = torch.FloatTensor(y_val).unsqueeze(0)
    X_test_tensor = torch.FloatTensor(X_test).unsqueeze(0)
    y_test_tensor = torch.FloatTensor(y_test).unsqueeze(0)
    
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    train_loader = DataLoader(train_dataset, batch_size=1, shuffle=False)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)
    
    input_size = X_train.shape[1]
    final_model = MultivariateLSTM(
        input_size=input_size,
        hidden_size=best_params['hidden_size'],
        num_layers=best_params['num_layers'],
        dropout=best_params['dropout']
    ).to(device)
    
    criterion = nn.MSELoss()
    optimizer = optim.Adam(final_model.parameters(), lr=best_params['learning_rate'])
    
    print("\n使用最佳参数重新训练模型...")
    train_losses, val_losses = train_model(final_model, train_loader, val_loader, criterion, optimizer, epochs=100)
    
    # 保存最终模型，使用ASIN命名
    model_filename = f'model/{asin}_lstm_model.pth'
    torch.save({
        'asin': asin,
        'model_state_dict': final_model.state_dict(),
        'best_params': best_params,
        'scaler_X': scaler_X,
        'scaler_y': scaler_y,
        'input_size': input_size
    }, model_filename)
    print(f"模型已保存到 {model_filename}")
    
    final_model.eval()
    test_predictions = []
    test_actuals = []
    
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            outputs = final_model(batch_X)
            test_predictions.extend(outputs.cpu().numpy().flatten())
            test_actuals.extend(batch_y.cpu().numpy().flatten())
    
    test_predictions = np.array(test_predictions).reshape(-1, 1)
    test_actuals = np.array(test_actuals).reshape(-1, 1)
    
    test_predictions_original = scaler_y.inverse_transform(test_predictions).flatten()
    test_actuals_original = scaler_y.inverse_transform(test_actuals).flatten()
    
    mse = np.mean((test_predictions_original - test_actuals_original) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(test_predictions_original - test_actuals_original))
    
    # 计算各种MAPE指标
    ape = np.abs((test_actuals_original - test_predictions_original) / test_actuals_original) * 100
    mape = np.mean(ape)
    median_ape = np.median(ape)  # 中位数APE，不受极端值影响
    
    # 找出极端APE值
    extreme_ape = ape > 100  # APE超过100%的情况
    extreme_count = extreme_ape.sum()
    
    print(f"\n最佳参数: {best_params}")
    print(f"MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, MAPE: {mape:.2f}%")
    
    plt.figure(figsize=(15, 6))
    plt.plot(range(len(test_actuals_original)), test_actuals_original, label='实际销量', linewidth=2)
    plt.plot(range(len(test_predictions_original)), test_predictions_original, label='预测销量', linewidth=2)
    plt.title('测试集预测结果')
    plt.xlabel('天数')
    plt.ylabel('销量')
    plt.legend()
    plt.grid(True)
    plt.show()

if __name__ == "__main__":
    excel_files = [
        'B0B41X9SMT.xlsx'
    ]
    
    for i, excel_file in enumerate(excel_files, 1):
        if os.path.exists(excel_file):
            main(excel_file)
        else:
            print(f"文件不存在: {excel_file}")