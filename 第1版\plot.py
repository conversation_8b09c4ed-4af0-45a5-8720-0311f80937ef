import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import math

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']

# 参数设置
sale = 100  # 销量基准

def linear(x, b):
    return b

# 为了演示，生成一个随机相位
sin_phase = np.random.uniform(0, 2 * math.pi)

def sin(x, A, w, c):
    return A * np.sin(w * x + sin_phase) + c

def poisson(x, lam):
    return np.random.poisson(lam)

days = range(120)

linear_data = [linear(day, sale) for day in days]
sin_data = [sin(day, sale/2, 0.1, sale) for day in days]
poisson_data = [poisson(day, sale) for day in days]

def plot1(days, linear_data, sin_data, poisson_data):
    plt.figure(figsize=(15, 7))
    plt.plot(days, linear_data, label='linear', linewidth=2)
    plt.plot(days, sin_data, label='sin', linewidth=2)
    plt.plot(days, poisson_data, label='poisson', linewidth=2)

    plt.title('销量曲线')
    plt.xlabel('天数')
    plt.ylabel('销量')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()

def plot2():
    df = pd.read_excel('B0B41X9SMT.xlsx')
    sales_data = df['销量'].values
    days = range(len(sales_data))
    
    plt.figure(figsize=(15, 7))
    plt.plot(days, sales_data, linewidth=2)
    plt.title('B0B41X9SMT销量数据')
    plt.xlabel('天数')
    plt.ylabel('销量')
    plt.grid(True, alpha=0.3)
    plt.show()

def plot3():
    df = pd.read_excel('B0BG1H99FB.xlsx')
    sales_data = df['销量'].values
    days = range(len(sales_data))
    
    plt.figure(figsize=(15, 7))
    plt.plot(days, sales_data, linewidth=2)
    plt.title('B0BG1H99FB销量数据')
    plt.xlabel('天数')
    plt.ylabel('销量')
    plt.grid(True, alpha=0.3)
    plt.show()

if __name__ == '__main__':
    # plot1(days, linear_data, sin_data, poisson_data)
    plot2()
    plot3()
